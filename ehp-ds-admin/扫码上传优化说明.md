# 扫码上传功能优化说明

## 优化目标

1. **用户扫码后立即关闭二维码弹框** - 提升用户体验，避免用户手动关闭弹框
2. **移动端上传后快速同步到PC端** - 减少同步延迟，提高响应速度
3. **二维码弹框关闭后3分钟轮询** - 确保文件同步完成，避免遗漏

## 优化实现

### 1. 移动端优化 (`src/views/mobile/upload.vue`)

#### 新增功能：
- **自动关闭二维码弹框**：页面加载完成后自动通知PC端关闭二维码弹框
- **快速同步触发**：上传成功后立即触发PC端同步检测

#### 关键代码：
```javascript
// 页面加载完成后通知PC端关闭二维码弹框
mounted() {
  this.notifyPCToCloseQrDialog()
},

// 通知PC端关闭二维码弹框
notifyPCToCloseQrDialog() {
  const mobileAccessFlag = {
    orderId: this.orderId,
    accessTime: Date.now(),
    action: 'close_qr_dialog'
  }
  localStorage.setItem('mobileQrAccess', JSON.stringify(mobileAccessFlag))
}

// 上传成功后立即触发PC端同步
const immediateSync = {
  orderId: this.orderId,
  timestamp: Date.now(),
  action: 'immediate_sync'
}
localStorage.setItem('immediateSyncTrigger', JSON.stringify(immediateSync))
```

### 2. PC端优化 (`src/views/order/sendgood.vue`)

#### 新增功能：
- **检测移动端访问**：实时检测移动端访问并立即关闭二维码弹框
- **3分钟轮询机制**：弹框关闭后继续轮询3分钟确保文件同步
- **快速响应**：轮询频率从2秒提升到1秒，提高响应速度

#### 关键代码：
```javascript
// 新增数据字段
data() {
  return {
    pollingStartTime: null, // 轮询开始时间
    pollingDuration: 3 * 60 * 1000, // 3分钟轮询时长
    qrDialogClosed: false // 二维码弹框是否已关闭
  }
}

// 优化轮询逻辑
checkUploadedFiles() {
  // 检查是否超过3分钟轮询时间
  const currentTime = Date.now()
  if (this.pollingStartTime && (currentTime - this.pollingStartTime > this.pollingDuration)) {
    clearInterval(this.pollingInterval)
    console.log('轮询已超过3分钟，自动停止')
    return
  }

  // 检测移动端访问，立即关闭二维码弹框
  const mobileAccessFlag = JSON.parse(localStorage.getItem('mobileQrAccess') || '{}')
  if (mobileAccessFlag.orderId === this.orderId && 
      mobileAccessFlag.action === 'close_qr_dialog' && 
      !this.qrDialogClosed) {
    this.qrDialogVisible = false
    this.qrDialogClosed = true
    localStorage.removeItem('mobileQrAccess')
  }

  // 检查立即同步触发器
  const immediateSyncTrigger = JSON.parse(localStorage.getItem('immediateSyncTrigger') || '{}')
  if (immediateSyncTrigger.orderId === this.orderId && 
      immediateSyncTrigger.action === 'immediate_sync') {
    this.syncFromServer()
    localStorage.removeItem('immediateSyncTrigger')
  }
}

// 优化弹框关闭处理
handleQrDialogClose() {
  this.qrDialogClosed = true
  
  // 不立即清除轮询，继续轮询3分钟
  if (!this.pollingInterval && !this.pollingStartTime) {
    this.pollingStartTime = Date.now()
    this.pollingInterval = setInterval(() => {
      this.checkUploadedFiles()
    }, 1000) // 每1秒检查一次
  }
  
  // 重置轮询计时器
  if (this.pollingStartTime) {
    this.pollingStartTime = Date.now()
  }
}
```

## 优化效果

### 用户体验提升：
1. **扫码即关闭**：用户扫码后无需手动关闭弹框，体验更流畅
2. **快速同步**：上传后1-2秒内即可在PC端看到图片，响应更快
3. **可靠保障**：3分钟轮询确保不会遗漏任何上传的文件

### 技术改进：
1. **轮询频率优化**：从2秒提升到1秒，响应速度提升50%
2. **智能检测**：通过localStorage实现跨页面通信，检测更准确
3. **时间控制**：精确的3分钟轮询控制，避免无限轮询

## 兼容性说明

- 保持了原有的localStorage同步机制，确保向后兼容
- 新增的优化功能不会影响现有的上传流程
- 支持多订单同时操作，通过orderId进行区分

## 使用说明

1. 用户点击"扫码上传"按钮生成二维码
2. 手机扫码后自动跳转到移动端上传页面，PC端二维码弹框自动关闭
3. 移动端上传图片后，PC端1-2秒内自动同步显示
4. 即使手动关闭二维码弹框，PC端也会继续轮询3分钟确保同步完成

## 应用页面

### 1. 单个订单发货页面 (`src/views/order/sendgood.vue`)
- ✅ 已完成优化
- 支持单个订单的扫码上传功能

### 2. 合并发货页面 (`src/views/order/merge.vue`)
- ✅ 已完成优化
- 支持多个订单合并发货的扫码上传功能
- 使用特殊的orderSn格式：`merge_${orderIds.join('_')}`

## 合并发货特殊处理

由于合并发货涉及多个订单，在实现上有以下特殊处理：

1. **订单ID检测**：使用 `this.orderIds.includes()` 检测是否匹配当前合并的订单组
2. **UUID获取**：使用第一个订单ID获取上传UUID
3. **二维码URL**：包含特殊标识 `isMerge=true` 和合并的orderSn
4. **服务器同步**：使用合并格式的orderSn获取图片列表

## 注意事项

- 确保移动端和PC端使用相同的域名，以便localStorage共享
- 轮询会在3分钟后自动停止，避免资源浪费
- 如果需要调整轮询时间，可修改`pollingDuration`参数
- 合并发货页面的orderSn格式为：`merge_${orderIds.join('_')}`
- 两个页面的优化逻辑完全一致，确保用户体验统一
