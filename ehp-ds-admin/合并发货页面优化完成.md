# 合并发货页面扫码上传优化完成

## 优化概述

已成功为合并发货页面 (`src/views/order/merge.vue`) 应用与单个订单发货页面相同的扫码上传优化逻辑，实现了：

1. ✅ **用户扫码后立即关闭二维码弹框**
2. ✅ **移动端上传后快速同步到PC端**  
3. ✅ **二维码弹框关闭后3分钟轮询保证同步**

## 主要修改内容

### 1. 数据字段新增
```javascript
data() {
  return {
    // ... 原有字段
    pollingStartTime: null, // 轮询开始时间
    pollingDuration: 3 * 60 * 1000, // 3分钟轮询时长
    qrDialogClosed: false // 二维码弹框是否已关闭
  }
}
```

### 2. 轮询机制优化
- **轮询频率**：从2秒提升到1秒，响应速度提升50%
- **时间控制**：精确的3分钟轮询控制，超时自动停止
- **状态管理**：通过 `pollingStartTime` 和 `qrDialogClosed` 管理轮询状态

### 3. 智能检测机制
- **移动端访问检测**：检测到移动端访问后立即关闭二维码弹框
- **立即同步触发**：移动端上传后立即触发PC端同步
- **多订单支持**：使用 `this.orderIds.includes()` 检测匹配的订单组

### 4. 生命周期管理
```javascript
beforeDestroy() {
  // 清除轮询
  if (this.pollingInterval) {
    clearInterval(this.pollingInterval)
  }
}
```

## 合并发货特殊处理

### 订单ID处理
- 使用 `this.orderIds` 数组管理多个订单ID
- 检测时使用 `this.orderIds.includes(orderId)` 匹配任一订单

### 二维码URL格式
```javascript
this.qrCodeUrl = `${window.location.origin}/upload-mobile?orderId=${this.orderIds[0]}&orderSn=merge_${this.orderIds.join('_')}&token=${token}&uuid=${this.uploadUuid}&isMerge=true`
```

### 服务器同步
```javascript
// 使用合并格式的orderSn获取图片列表
const response = await api_order.getInvoiceImgList(`merge_${this.orderIds.join('_')}`)
```

## 优化效果对比

| 功能 | 优化前 | 优化后 |
|------|--------|--------|
| 扫码后弹框关闭 | 需手动关闭 | 自动关闭 |
| 轮询频率 | 2秒 | 1秒 |
| 轮询时长 | 手动关闭后停止 | 关闭后继续3分钟 |
| 同步速度 | 2-4秒 | 1-2秒 |
| 移动端检测 | 无 | 实时检测 |

## 代码一致性

合并发货页面的优化逻辑与单个订单发货页面完全一致：

- ✅ 相同的轮询机制
- ✅ 相同的检测逻辑  
- ✅ 相同的时间控制
- ✅ 相同的用户体验

唯一区别是针对多订单的特殊处理，确保功能正常运行。

## 测试建议

1. **基础功能测试**
   - 扫码生成二维码
   - 移动端访问后PC端弹框自动关闭
   - 移动端上传图片后PC端快速同步

2. **多订单场景测试**
   - 选择多个订单进行合并发货
   - 验证二维码URL包含正确的订单信息
   - 确认图片上传到正确的合并订单

3. **轮询机制测试**
   - 手动关闭弹框后验证继续轮询3分钟
   - 验证3分钟后轮询自动停止
   - 测试页面销毁时轮询正确清理

## 总结

合并发货页面的扫码上传优化已全部完成，与单个订单发货页面保持一致的用户体验。用户在使用合并发货功能时，将享受到：

- **更流畅的操作体验**：扫码后无需手动关闭弹框
- **更快的同步速度**：1-2秒内看到上传结果
- **更可靠的保障机制**：3分钟轮询确保不遗漏文件

所有优化均已完成并可投入使用！
