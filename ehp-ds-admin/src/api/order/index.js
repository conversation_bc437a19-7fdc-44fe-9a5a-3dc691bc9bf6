import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/order/list',
    method: 'get',
    params: data
  })
}

export function detail(orderId) {
  return request({
    url: '/order/details/' + orderId,
    method: 'get'
  })
}

export function company() {
  return request({
    url: '/order/logistics/company',
    method: 'get'
  })
}

export function confirm(status, data) {
  return request({
    url: '/order/confirm/' + status,
    method: 'post',
    data
  })
}

export function sendGood(data) {
  return request({
    url: '/order/delivery/' + data,
    method: 'get'
  })
}

export function print(orderId) {
  return request({
    url: '/order/print/' + orderId,
    method: 'get'
  })
}

export function orderexport() {
  return process.env.VUE_APP_BASE_API + '/order/export?'
}

export function outGoods(data) {
  return request({
    url: '/order',
    method: 'post',
    data
  })
}

export function saveInvoice(data) {
  return request({
    url: '/order/invoice/save/' + data.orderSn,
    method: 'post',
    params: { fileUrl: data.fileUrl }
  })
}

// 可合并发货订单列表
export function getMergeOrderList() {
  return request({
    url: '/order/merge/delivery',
    method: 'get'
  })
}

// 订单合并发货
export function mergeOrders(data, expressId, deliveryId) {
  return request({
    url: `/order/delivery/${expressId}/${deliveryId}`,
    method: 'post',
    data
  })
}

// 地区
export function citylist() {
  return request({
    url: '/common/city/list',
    method: 'get'
  })
}

// 收货信息变更
export function update(data) {
  return request({
    url: '/order/update',
    method: 'post',
    data
  })
}

// 获取上传发货凭证的uuid
export function getUuid(orderSn) {
  return request({
    url: `/order/shipping/img/uuid/${orderSn}`,
    method: 'get'
  })
}

// 获取上传发货图片的uuid(多订单号)
export function getMergeUuid(orderSnList) {
  return request({
    url: '/order/shipping/img/uuid',
    method: 'post',
    data: {
      orderSnList: orderSnList
    }
  })
}

// 上传发货凭证图片
export function uploadInvoiceImg(data, uuid) {
  return request({
    url: `/order/shipping/img/upload/${uuid}`,
    method: 'post',
    data
  })
}

// 获取发货凭证图片列表
export function getInvoiceImgList(orderSn) {
  return request({
    url: `/order/shipping/img/${orderSn}`,
    method: 'get'
  })
}

// 删除发货凭证图片
export function deleteInvoiceImg(orderSn, id) {
  return request({
    url: `/order/shipping/img/${orderSn}/${id}`,
    method: 'delete'
  })
}

// 获取扫码状态
export function checkMobileAccess(uuid) {
  return request({
    url: `/order/shipping/img/scan/${uuid}`,
    method: 'get'
  })
}

// 上报扫码状态（默认已扫码）
export function notifyMobileUpload(uuid) {
  return request({
    url: `/order/shipping/img/scan/${uuid}`,
    method: 'post'
  })
}

export default {
  list,
  detail,
  company,
  confirm,
  sendGood,
  print,
  orderexport,
  outGoods,
  saveInvoice,
  getMergeOrderList,
  mergeOrders,
  citylist,
  update,
  getUuid,
  getMergeUuid,
  uploadInvoiceImg,
  getInvoiceImgList,
  deleteInvoiceImg,
  checkMobileAccess,
  notifyMobileUpload
}
