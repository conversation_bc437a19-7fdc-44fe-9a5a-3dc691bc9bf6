<template>
  <div class="mobile-upload-container">
    <!-- 错误状态显示 -->
    <div v-if="!isValidParams" class="error-container">
      <div class="error-icon">
        <i class="el-icon-warning-outline"></i>
      </div>
      <h3>页面访问异常</h3>
      <p>{{ errorMessage }}</p>
      <p class="error-tips">请通过PC端正确的二维码扫描进入此页面</p>
    </div>

    <!-- 正常上传界面 -->
    <div v-else>
      <div class="header">
        <h2>发货凭证上传</h2>
        <p>订单号：{{ orderSn || orderId }}</p>
        <p>请选择要上传的发货凭证图片</p>
        <div class="upload-progress">
          <span class="progress-text">已上传：{{ existingFiles.length + fileList.length }}/4 张</span>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: ((existingFiles.length + fileList.length) / 4 * 100) + '%' }"></div>
          </div>
        </div>
      </div>

      <!-- 已有图片显示 -->
      <div v-if="existingFiles.length > 0" class="existing-files">
        <h3>已上传的图片</h3>
        <div class="existing-images">
          <div v-for="(file, index) in existingFiles" :key="'existing-' + index" class="existing-image-item">
            <img :src="file.url" :alt="file.name" @click="previewImage(file.url)" />
            <div class="image-name">{{ file.name }}</div>
          </div>
        </div>
      </div>

      <div class="upload-area">
        <el-upload
          v-show="existingFiles.length + fileList.length < 4"
          ref="mobileUpload"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :file-list="fileList"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :limit="4"
          :on-exceed="handleExceed"
          accept="image/*"
          list-type="picture-card"
          :auto-upload="true"
          :multiple="true"
          name="files"
          class="mobile-uploader"
        >
          <i class="el-icon-plus upload-icon"></i>
          <div class="upload-text">选择图片</div>
        </el-upload>

        <!-- 已达到上传上限的提示 -->
        <div v-if="existingFiles.length + fileList.length >= 4" class="upload-complete">
          <i class="el-icon-success upload-complete-icon"></i>
          <div class="upload-complete-text">已完成上传 (4/4)</div>
          <p class="upload-complete-tips">所有图片已上传完成，请返回PC端查看</p>
        </div>
      </div>

      <div class="tips">
        <h3>上传须知：</h3>
        <ul>
          <li>支持格式：.png、.jpg、.jpeg</li>
          <li>文件大小：单个文件不超过4MB</li>
          <li>数量限制：最多上传4张图片</li>
          <li>支持多选：可一次选择多个文件</li>
        </ul>
      </div>

      <!-- <div class="actions">
        <el-button
          type="primary"
          size="large"
          :disabled="fileList.length === 0"
          class="confirm-btn"
          @click="confirmUpload"
        >
          确认上传 ({{ fileList.length }}/4)
        </el-button>
      </div> -->

      <!-- 图片预览弹窗 -->
      <el-dialog :visible.sync="previewDialogVisible" width="90%" center>
        <img :src="previewImageUrl" style="width: 100%;" alt="预览图片" />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api_order from '@/api/order/index'
export default {
  name: 'MobileUpload',

  data() {
    return {
      orderId: '',
      orderSn: '',
      token: '',
      fileList: [],
      existingFiles: [], // 已有的图片列表
      uploadUrl: '',
      uploadHeaders: {},
      isValidParams: false,
      errorMessage: '',
      uploadUuid: '',
      previewDialogVisible: false,
      previewImageUrl: ''
    }
  },

  async created() {
    await this.initParams()
  },

  methods: {
    async initParams() {
      try {
        // 从URL参数获取orderId、token和uuid
        const urlParams = new URLSearchParams(window.location.search)
        this.orderId = urlParams.get('orderId')
        this.orderSn = urlParams.get('orderSn')
        this.token = urlParams.get('token')
        this.uploadUuid = urlParams.get('uuid')

        // 参数验证
        if (!this.orderId) {
          this.errorMessage = '缺少订单ID参数'
          return
        }

        if (!this.orderSn) {
          this.errorMessage = '缺少订单号参数'
          return
        }

        if (!this.token) {
          this.errorMessage = '缺少认证token参数'
          return
        }

        if (!this.uploadUuid) {
          this.errorMessage = '缺少上传权限参数'
          return
        }

        // 配置上传参数
        this.configureUpload()

        this.isValidParams = true
        console.log('移动端上传页面初始化成功:', {
          orderId: this.orderId,
          orderSn: this.orderSn,
          hasToken: !!this.token,
          uuid: this.uploadUuid
        })

        // 获取已有的图片列表
        await this.getExistingImages()

        // 初始化完成后，上报扫码状态
        await this.notifyPCToCloseQrDialog()
      } catch (error) {
        console.error('初始化参数失败:', error)
        this.errorMessage = '页面初始化失败：' + (error.message || '未知错误')
      }
    },

    // 配置上传参数
    configureUpload() {
      if (!this.uploadUuid) {
        throw new Error('上传UUID不存在')
      }
      this.uploadUrl = `${process.env.VUE_APP_BASE_API}/order/shipping/img/upload/${this.uploadUuid}`
      this.uploadHeaders = {
        Authorization: this.token
      }
      console.log('移动端配置上传参数:', { uploadUrl: this.uploadUrl, uuid: this.uploadUuid })
    },

    // 获取已有的图片列表
    async getExistingImages() {
      try {
        if (!this.orderSn) return

        console.log('移动端准备获取图片列表:', {
          orderSn: this.orderSn,
          token: this.token ? '有token' : '无token',
          tokenLength: this.token ? this.token.length : 0
        })

        // 使用request工具发送请求，手动设置Authorization头
        // 注意：移动端的token需要手动设置，因为不在store中
        const response = await request({
          url: `/order/shipping/img/${this.orderSn}`,
          method: 'get',
          headers: {
            'Authorization': this.token
          }
        })

        if (response && response.length > 0) {
          this.existingFiles = response.map(item => ({
            id: item.id,
            name: item.fileName || item.name,
            url: item.path,
            uid: item.id || item.uid
          }))
          console.log('移动端获取已有图片列表成功:', this.existingFiles)
        } else {
          console.log('移动端未获取到图片列表或列表为空')
        }
      } catch (error) {
        console.error('移动端获取已有图片列表失败:', error)
        // 不影响正常上传功能，只是无法显示已有图片

        // 如果是401错误，可能是token无效
        if (error.response && error.response.status === 401) {
          console.warn('移动端token可能无效，无法获取图片列表')
        }
      }
    },

    beforeUpload(file) {
      if (!this.isValidParams) {
        this.$message.error('页面参数异常，无法上传')
        return false
      }

      const isImage = file.type.startsWith('image/')
      const isValidType = ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)
      const isLt4M = file.size / 1024 / 1024 < 4

      if (!isImage || !isValidType) {
        this.$message.error('只支持上传 .png、.jpg、.jpeg 格式的图片文件!')
        return false
      }

      if (!isLt4M) {
        this.$message.error(`上传图片"${file.name}"大小不能超过 4MB!`)
        return false
      }

      // 检查是否会超过总数限制（包括已有图片）
      const totalCount = this.existingFiles.length + this.fileList.length
      if (totalCount >= 4) {
        this.$message.error('最多只能上传4张图片!')
        return false
      }

      return true
    },

    async handleUploadSuccess(response, file, fileList) {

      if (response.code === 0 || response.success) {
        this.$message.success(`图片"${file.name}"上传成功`)

        // 获取上传成功的文件URL
        let fileUrl = ''
        let matchedFileData = null

        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          console.log('服务器返回的文件数据:', response.data)

          // 尝试多种匹配方式找到对应的文件
          matchedFileData = response.data.find(item => {
            const nameMatch = item.originalName === file.name ||
                             item.fileName === file.name ||
                             item.name === file.name
            console.log(`匹配检查: ${item.originalName || item.fileName || item.name} vs ${file.name} = ${nameMatch}`)
            return nameMatch
          })

          if (matchedFileData) {
            fileUrl = matchedFileData.path
            console.log('找到匹配文件:', matchedFileData)
          } else {
            // 如果找不到匹配的文件，取最后一个（通常是刚上传的）
            matchedFileData = response.data[response.data.length - 1]
            fileUrl = matchedFileData.path
            console.log('未找到匹配文件，使用最后一个:', matchedFileData)
          }
        } else {
          // 兼容旧格式
          fileUrl = response.files && response.files.file || response.fileUrl || response.url
          console.log('使用兼容格式URL:', fileUrl)
        }

        // 处理图片URL，确保是完整的可访问路径
        let fullImageUrl = fileUrl
        if (fileUrl && !fileUrl.startsWith('http')) {
          // 如果不是完整URL，添加基础URL前缀
          fullImageUrl = process.env.VUE_APP_BASE_API + fileUrl
        }

        // 立即将上传成功的图片添加到已上传列表中
        if (fullImageUrl) {
          const newExistingFile = {
            id: (matchedFileData && matchedFileData.id) || file.uid || Date.now(),
            name: file.name,
            url: fullImageUrl,
            uid: file.uid || (matchedFileData && matchedFileData.id) || Date.now()
          }

          // 添加到已上传列表
          this.existingFiles.push(newExistingFile)

          // 从当前上传列表中移除该文件（避免重复显示）
          const index = this.fileList.findIndex(f => f.uid === file.uid)
          if (index > -1) {
            this.fileList.splice(index, 1)
          }

          // 强制触发视图更新
          this.$forceUpdate()
        } else {
          console.error('无法获取有效的文件URL')
        }

        // 文件上传成功，通知PC端有新图片上传
        try {
          console.log('文件上传成功:', fullImageUrl)

          // 通知PC端有新图片上传（可选：通过localStorage设置标识）
          this.notifyPCNewImageUploaded(file.name)

          // 检查是否已达到上传限制
          const totalCount = this.existingFiles.length + this.fileList.length
          if (totalCount >= 4) {
            this.$message.info('已达到最大上传数量(4张)')
            // 禁用上传组件
            this.$nextTick(() => {
              const uploadElement = this.$refs.mobileUpload && this.$refs.mobileUpload.$el && this.$refs.mobileUpload.$el.querySelector('.el-upload')
              if (uploadElement) {
                uploadElement.style.display = 'none'
              }
            })
          }
        } catch (error) {
          console.error('存储文件信息失败:', error)
          this.$message.error('文件信息存储失败')
        }
      } else {
        this.$message.error(response.msg || response.message || `图片"${file.name}"上传失败`)
      }
    },

    handleUploadError(error, file, fileList) {
      console.error('上传失败:', error)
      this.$message.error(`图片"${file.name}"上传失败，请重试`)

      // 从文件列表中移除失败的文件
      const index = this.fileList.findIndex(f => f.uid === file.uid)
      if (index > -1) {
        this.fileList.splice(index, 1)
      }
    },

    handleExceed(files, fileList) {
      const exceedCount = files.length
      const totalCount = this.existingFiles.length + this.fileList.length
      const remainingSlots = 4 - totalCount
      this.$message.warning(`最多只能上传4张图片，您选择了${exceedCount}张，当前还可以上传${remainingSlots}张`)
    },

    previewImage(url) {
      this.previewImageUrl = url
      this.previewDialogVisible = true
    },

    confirmUpload() {
      const totalCount = this.existingFiles.length + this.fileList.length
      if (totalCount > 0) {
        this.$message.success('所有图片已上传完成！请返回PC端查看')
        // 延迟关闭页面
        setTimeout(() => {
          try {
            window.close()
          } catch (error) {
            // 如果无法关闭窗口，提示用户手动返回
            this.$message.info('请手动返回PC端查看上传结果')
          }
        }, 2000)
      } else {
        this.$message.warning('请先选择要上传的图片')
      }
    },

    // 新增：刷新已上传图片列表
    async refreshExistingImages() {
      try {
        await this.getExistingImages()
        console.log('已刷新图片列表')
      } catch (error) {
        console.error('刷新图片列表失败:', error)
      }
    },

    // 通知PC端关闭二维码弹框
    async notifyPCToCloseQrDialog() {
      try {
        if (this.uploadUuid) {
          // 通过服务器API上报扫码状态（默认已扫码）
          await this.reportScanStatus()
        } else {
          console.warn('uploadUuid为空，无法上报扫码状态')
        }
      } catch (error) {
        console.error('通知PC端关闭弹框失败:', error)
      }
    },

    // 上报扫码状态
    async reportScanStatus() {
      try {
        if (!this.uploadUuid) {
          console.warn('uploadUuid为空，无法上报扫码状态')
          return
        }
        // 调用API上报扫码状态，status=1表示已扫码
        const response = await api_order.notifyMobileUpload(this.uploadUuid, 1)
        console.log('已上报扫码状态:', response)
      } catch (error) {
        console.error('上报扫码状态失败:', error)
      }
    },

    // 通知PC端有新图片上传
    notifyPCNewImageUploaded(fileName) {
      try {
        // 通过localStorage设置新图片上传标识，PC端可以检测到
        const uploadNotification = {
          orderId: this.orderId,
          orderSn: this.orderSn,
          fileName: fileName,
          uploadTime: Date.now(),
          action: 'new_image_uploaded'
        }
        localStorage.setItem('newImageUploadNotification', JSON.stringify(uploadNotification))
        console.log('已通知PC端有新图片上传:', uploadNotification)
      } catch (error) {
        console.error('通知PC端新图片上传失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.mobile-upload-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  background: white;
  border-radius: 8px;
  padding: 40px 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.error-icon {
  font-size: 64px;
  color: #f56c6c;
  margin-bottom: 20px;
}

.error-container h3 {
  color: #333;
  font-size: 20px;
  margin-bottom: 15px;
}

.error-container p {
  color: #666;
  font-size: 16px;
  margin-bottom: 10px;
  line-height: 1.5;
}

.error-tips {
  color: #999 !important;
  font-size: 14px !important;
  margin-top: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.header p {
  color: #666;
  font-size: 14px;
}

.upload-progress {
  margin-bottom: 10px;
}

.progress-text {
  font-size: 14px;
  color: #666;
}

.progress-bar {
  height: 10px;
  background-color: #f0f0f0;
  border-radius: 5px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #409EFF;
  transition: width 0.3s;
}

.upload-area {
  margin-bottom: 30px;
}

.mobile-uploader {
  width: 100%;
}

.mobile-uploader /deep/ .el-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  width: 100%;
  height: 200px;
  position: relative;
  overflow: hidden;
  background: white;
  transition: all 0.3s;
}

.mobile-uploader /deep/ .el-upload:hover {
  border-color: #409EFF;
}

.upload-icon {
  font-size: 40px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.upload-text {
  color: #999;
  font-size: 16px;
}

.mobile-uploader /deep/ .el-upload-list--picture-card .el-upload-list__item {
  width: 100%;
  height: 150px;
  margin: 10px 0;
  border-radius: 8px;
}

.tips {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tips h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
}

.tips ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips li {
  color: #666;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.tips li:before {
  content: "•";
  color: #409EFF;
  position: absolute;
  left: 0;
  top: 0;
}

.actions {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
}

.confirm-btn {
  width: 100%;
  height: 50px;
  font-size: 18px;
  border-radius: 25px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .mobile-upload-container {
    padding: 15px;
  }

  .header h2 {
    font-size: 20px;
  }

  .mobile-uploader /deep/ .el-upload {
    height: 180px;
  }

  .upload-icon {
    font-size: 36px;
  }

  .upload-text {
    font-size: 14px;
  }
}

/* 已有图片显示样式 */
.existing-files {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.existing-files h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
}

.existing-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
}

.existing-image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  background: #f9f9f9;
}

.existing-image-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 5px;
}

.existing-image-item img:hover {
  opacity: 0.8;
}

.image-name {
  font-size: 12px;
  color: #666;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

.upload-complete {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: white;
  margin-bottom: 30px;
}

.upload-complete-icon {
  font-size: 64px;
  color: #409EFF;
  margin-bottom: 10px;
}

.upload-complete-text {
  color: #333;
  font-size: 18px;
  margin-bottom: 10px;
}

.upload-complete-tips {
  color: #666;
  font-size: 14px;
}
</style>
